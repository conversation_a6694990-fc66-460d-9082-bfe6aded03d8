# frozen_string_literal: true

# Serializer for RemoteUser instances
# Handles serialization of user data fetched from Core service via GRPC
class RemoteUserSerializer
  include JSONAPI::Serializer

  set_type :remote_user

  attributes :id, :name, :email, :status, :avatar_url

  # Include user roles information from GRPC response
  has_many :user_roles, serializer: AtharAuth::Models::UserRoleSerializer do |object|
    object.user_roles || []
  end
end
