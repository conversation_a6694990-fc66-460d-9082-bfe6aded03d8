# frozen_string_literal: true

module RemoteUsers
  # Struct for handling avatar attributes from Core service
  class AvatarStruct
    include ActiveModel::Model
    include ActiveModel::Attributes

    attribute :url, :string
    attribute :filename, :string
    attribute :content_type, :string
    attribute :size, :integer

    def initialize(attributes = {})
      super(attributes)
    end

    def present?
      url.present?
    end

    def blank?
      !present?
    end

    def empty?
      !present?
    end

    def as_json(options = {})
      {
        url: url,
        filename: filename,
        content_type: content_type,
        size: size
      }.compact
    end

    def to_h
      as_json
    end
  end
end
