class Case < ApplicationRecord
  # include Athar::Commons::Models::Concerns::Ransackable  # Temporarily disabled due to Rails 8.0 compatibility
  include Athar::Commons::Models::Concerns::ActsAsApprovable
  include Athar::Commons::ActiveStruct::ActiveRecordAssociations
  include AtharRpc::ActiveRpc
  # include Case::ActiveRpcConcern
  acts_as_approvable

  # RPC associations for user data from Core service
  rpc_belongs_to :assigned_user, foreign_key: :assigned_user_id, class_name: 'RemoteUser'
  rpc_belongs_to :created_by_user, foreign_key: :created_by_id, class_name: 'RemoteUser'

  # Associations
  # User associations handled via rpc_belongs_to (fetches data from Core service via GRPC)
  # Note: Services are now a generic catalog, not tied to specific cases
  has_many :form_submissions, dependent: :destroy
  has_many :comments, dependent: :destroy
  has_many :case_comments, -> { case_level }, class_name: "Comment"
  has_many :field_comments, -> { field_level }, class_name: "Comment"
  has_many :case_documents, dependent: :destroy

  # Approval system integration
  acts_as_approvable

  # Beneficiary data is stored in individual database columns for performance and search
  # Individual columns: beneficiary_name, beneficiary_age, beneficiary_gender,
  # beneficiary_nationality, beneficiary_phone, beneficiary_id_number,
  # beneficiary_date_of_birth, beneficiary_city

  # Validations
  validates :status, presence: true
  validates :assigned_user_id, presence: true
  validates :created_by_id, presence: true
  validates :project_id, presence: true
  validates :case_number, presence: true, uniqueness: true
  validate :started_at_not_in_future
  validate :closed_at_after_started_at
  validate :closed_at_required_for_closed_status

  # Callbacks
  before_validation :set_default_status, on: :create
  before_validation :set_started_at, on: :create
  after_update :set_closed_at, if: :saved_change_to_status?

  # Enums
  enum :status, {
    case_draft: "draft",
    case_ready_for_approval: "ready_for_approval",
    case_pending_approval: "pending_approval",
    case_approved: "approved",
    case_active: "active",
    case_closed: "closed",
    case_suspended: "suspended",
    case_transferred: "transferred"
  }

  enum :case_type, {
    general: "general",
    protection: "protection",
    child_protection: "child_protection",
    gbv: "gbv",
    education: "education",
    health: "health",
    livelihood: "livelihood"
  }

  enum :priority_level, {
    low: 1,
    medium: 2,
    high: 3,
    urgent: 4,
    critical: 5
  }

  enum :confidentiality_level, {
    public_access: 0,
    restricted: 1,
    confidential: 2
  }, default: :public_access

  enum :approval_status, {
    pending: "pending",
    approved: "approved",
    rejected: "rejected"
  }

  # Scopes (enum automatically creates scopes for status values)
  scope :in_progress, -> { where(status: [ "draft", "ready_for_approval", "pending_approval", "approved", "active" ]) }
  scope :by_assigned_user, ->(assigned_user_id) { where(assigned_user_id: assigned_user_id) if assigned_user_id.present? }
  scope :by_case_type, ->(type) { where(case_type: type) if type.present? }
  scope :by_priority, ->(priority) { where(priority_level: priority) if priority.present? }
  scope :recent, -> { order(created_at: :desc) }

  # Callbacks
  before_validation :generate_case_number, on: :create
  before_save :update_beneficiary_data!
  after_create :create_initial_form_submissions

  # Ransack configuration
  def self.ransackable_attributes(auth_object = nil)
    %w[status approval_status started_at closed_at created_at updated_at assigned_user_id project_id case_number case_type priority_level confidentiality_level beneficiary_name beneficiary_age beneficiary_gender beneficiary_nationality]
  end

  def self.ransackable_associations(auth_object = nil)
    %w[form_submissions comments case_documents]
  end

  # User data access methods are now provided by Case::ActiveRpcConcern

  # Approval system integration
  def approval_action
    "case_approval"
  end

  def system_name
    "case-manager"
  end

  def approval_context
    {
      case_id: id,
      case_number: case_number,
      case_type: case_type,
      priority_level: priority_level,
      beneficiary_name: beneficiary_name,
      assigned_user_id: assigned_user_id,
      project_id: project_id,
      created_by_id: created_by_id,
      form_completion_status: form_completion_summary
    }
  end

  def on_approval_status_change(new_status, previous_status)
    Rails.logger.info("Case approval changed from #{previous_status} to #{new_status}")
    case new_status.to_s
    when "approved"
      handle_case_approval_granted
    when "rejected"
      handle_case_approval_rejected
    end
  end

  # Progressive Form Completion Methods
  def form_completion_summary
    form_templates = FormTemplate.where(project_id: project_id).active.ordered

    form_templates.map do |template|
      submission = form_submissions.find_by(form_template: template)
      {
        template_id: template.id,
        template_name: template.name,
        template_title: template.title,

        sequence_order: template.sequence_order,
        status: submission&.status || "not_started",
        completion_percentage: submission&.completion_percentage || 0,
        can_start: can_start_form?(template),
        prerequisites_met: template.prerequisites_met?(self)
      }
    end
  end

  def can_start_form?(form_template)
    return false unless form_template.active?
    return false unless form_template.prerequisites_met?(self)

    # Check if user has permission to access this form
    true # Will be enhanced with user context
  end

  def next_required_form
    form_templates = FormTemplate.where(project_id: project_id).active.ordered

    form_templates.find do |template|
      submission = form_submissions.find_by(form_template: template)
      next if submission&.form_completed?

      template.prerequisites_met?(self)
    end
  end

  def overall_completion_percentage
    total_forms = FormTemplate.where(project_id: project_id).active.count
    return 0 if total_forms.zero?

    completed_forms = form_submissions.form_completed.count
    (completed_forms.to_f / total_forms * 100).round(2)
  end

  def can_submit_for_approval?
    return false unless case_ready_for_approval? || case_draft?

    # All required forms must be completed
    required_forms = FormTemplate.where(project_id: project_id).active
    required_forms.all? do |template|
      submission = form_submissions.find_by(form_template: template)
      submission&.form_completed?
    end
  end

  # Instance methods
  def age_in_days
    return nil unless started_at

    end_date = closed_at || Date.current
    (end_date.to_date - started_at.to_date).to_i
  end

  def is_active?
    %w[case_active case_approved].include?(status)
  end

  def is_closed?
    status == "case_closed"
  end

  def is_suspended?
    status == "case_suspended"
  end

  def requires_approval?
    case_pending_approval?
  end

  def is_approved?
    case_approved?
  end

  def is_rejected?
    approval_status == "rejected"
  end

  # Service planning status methods (renamed from case plan logic)
  def has_service_plan?
    service_planning_complete?
  end

  def service_planning_status
    return "not_started" unless consent_assent_complete?
    return "in_progress" unless service_planning_complete?
    return "completed" if service_planning_complete?
  end

  def planning_progress_percentage
    form_completion_progress[:percentage]
  end

  def case_summary
    {
      id: id,
      case_number: case_number,
      case_type: case_type,
      priority_level: priority_level,
      confidentiality_level: confidentiality_level,
      beneficiary_name: beneficiary_name,
      assigned_user_id: assigned_user_id,
      status: status,
      approval_status: approval_status,
      age_days: age_in_days,
      # Note: Services are now a generic catalog, not tied to specific cases
      form_completion_percentage: overall_completion_percentage,
      comments_count: comments.count,
      unresolved_comments_count: comments.open.count,
      documents_count: case_documents.count
    }
  end

  def can_be_closed?
    return false unless is_active?
    return false unless comments.open.empty? # All comments must be resolved
    # In the new architecture, closure is based on form completion, not service status
    return false unless form_completion_progress[:can_submit]

    true
  end

  def close_case!(closed_by_user_id)
    return false unless can_be_closed?

    update!(
      status: "closed",
      closed_at: Time.current,
      notes: "#{notes}\n\nClosed by user #{closed_by_user_id} at #{Time.current}"
    )
  end

  def submit_for_approval!(user_id)
    return false unless can_submit_for_approval?

    update!(status: :ready_for_approval)
    submit_for_approval(user_id)
  end

  # Note: Services are now a generic catalog, not tied to specific cases

  # Progressive form completion methods (from architectural plan)
  def can_submit_for_approval?
    consent_assent_complete? && registration_complete? && assessment_complete? && service_planning_complete?
  end

  def consent_assent_complete?
    form_completed?('consent_assent')
  end

  def registration_complete?
    form_completed?('registration_rapid_assessment')
  end

  def assessment_complete?
    form_completed?('comprehensive_assessment')
  end

  def service_planning_complete?
    form_completed?('service_planning')
  end

  def form_completed?(form_name)
    form_submissions.joins(:form_template)
                   .where(form_templates: { name: form_name })
                   .where(status: 'form_submitted').exists?
  end

  def completed_form_names
    form_submissions.joins(:form_template)
                   .where(status: 'form_submitted')
                   .pluck('form_templates.name')
  end

  def form_completion_progress
    required_forms = [ 'consent_assent', 'registration_rapid_assessment', 'comprehensive_assessment', 'case_plan_implementation' ]
    completed_forms = completed_form_names & required_forms

    {
      total: required_forms.length,
      completed: completed_forms.length,
      percentage: (completed_forms.length.to_f / required_forms.length * 100).round,
      next_required: (required_forms - completed_forms).first,
      can_submit: completed_forms.length == required_forms.length,
      completed_forms: completed_forms,
      remaining_forms: required_forms - completed_forms
    }
  end

  def overall_completion_percentage
    form_completion_progress[:percentage]
  end

  private

  def generate_case_number
    return if case_number.present?

    # Generate case number: PROJ-YYYY-NNNN
    year = Date.current.year
    project_prefix = project_id.to_s.last(3).upcase

    last_case = Case.where(project_id: project_id)
                   .where("case_number LIKE ?", "#{project_prefix}-#{year}-%")
                   .order(:case_number)
                   .last

    if last_case && last_case.case_number.match(/#{project_prefix}-#{year}-(\d+)/)
      sequence = $1.to_i + 1
    else
      sequence = 1
    end

    self.case_number = "#{project_prefix}-#{year}-#{sequence.to_s.rjust(4, '0')}"
  end

  # Beneficiary data methods (replaces Beneficiary model)
  def registration_form
    form_submissions.joins(:form_template)
                   .where(form_templates: { name: "registration_rapid_assessment" })
                   .first
  end

  # Update beneficiary data from registration form
  def update_beneficiary_data!
    return unless registration_form

    form_data = registration_form.form_data
    child_details = form_data["child_personal_details"] || {}

    self.beneficiary_name = "#{child_details['first_name']} #{child_details['last_name']}".strip
    self.beneficiary_age = child_details["age"]
    self.beneficiary_gender = child_details["sex"]
    self.beneficiary_nationality = child_details["nationality_status"]
    self.beneficiary_phone = form_data["care_arrangements"]&.dig("contact_phone")
    self.beneficiary_id_number = child_details["identification_number"]
    self.beneficiary_date_of_birth = child_details["date_of_birth"]
    self.beneficiary_city = form_data["care_arrangements"]&.dig("current_address")

    save!
  end

  # Beneficiary helper methods
  def beneficiary_is_minor?
    beneficiary_age && beneficiary_age < 18
  end

  def beneficiary_full_profile
    {
      name: beneficiary_name,
      age: beneficiary_age,
      gender: beneficiary_gender,
      nationality: beneficiary_nationality,
      phone: beneficiary_phone,
      id_number: beneficiary_id_number,
      date_of_birth: beneficiary_date_of_birth,
      city: beneficiary_city,
      is_minor: beneficiary_is_minor?
    }
  end

  def create_initial_form_submissions
    return unless persisted?

    # Create draft submissions for all required forms
    FormTemplate.where(project_id: project_id).active.each do |template|
      form_submissions.find_or_create_by(form_template: template) do |submission|
        submission.created_by_id = created_by_id
        submission.status = :form_draft
      end
    end
  end

  def handle_case_approval_granted
    update!(status: :case_active) if case_ready_for_approval? || case_pending_approval?
  end

  def handle_case_approval_rejected
    update!(status: :case_draft)
  end

  def set_default_status
    self.status ||= "draft"
  end

  def set_started_at
    self.started_at ||= Time.current
  end

  def set_closed_at
    if status == "closed" && closed_at.blank?
      self.closed_at = Time.current
    elsif status != "closed"
      self.closed_at = nil
    end
  end

  def started_at_not_in_future
    return unless started_at

    if started_at > Time.current
      errors.add(:started_at, "cannot be in the future")
    end
  end

  def closed_at_after_started_at
    return unless started_at && closed_at

    if closed_at < started_at
      errors.add(:closed_at, "cannot be before started date")
    end
  end

  def closed_at_required_for_closed_status
    if status == "closed" && closed_at.blank?
      errors.add(:closed_at, "is required when case is closed")
    end
  end
end
