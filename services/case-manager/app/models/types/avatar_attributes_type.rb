# frozen_string_literal: true

require_relative '../remote_users/avatar_struct'

module Types
  # Custom ActiveModel type specifically for handling avatar_attributes
  # This type allows storing avatar data from gRPC responses
  class AvatarAttributesType < ActiveModel::Type::Value
    # Cast input to the appropriate format
    # Convert input to AvatarStruct
    def cast(value)
      return nil if value.nil?

      # If it's already an AvatarStruct, return it
      return value if value.is_a?(RemoteUsers::AvatarStruct)

      # If it's a hash, convert it to AvatarStruct
      if value.is_a?(Hash)
        return RemoteUsers::AvatarStruct.new(value)
      end

      # If it's a gRPC response object, convert to hash first
      if value.respond_to?(:to_hash)
        return RemoteUsers::AvatarStruct.new(value.to_hash)
      end

      # Default fallback
      RemoteUsers::AvatarStruct.new
    end

    # Serialize the value for JSON representation
    def serialize(value)
      return {} if value.nil?

      # Convert to hash if it's an AvatarStruct
      if value.is_a?(RemoteUsers::AvatarStruct)
        return value.as_json
      end

      # Process the hash
      if value.is_a?(Hash)
        return value
      end

      # Default fallback
      {}
    end

    # Deserialize from database format to Ruby object
    def deserialize(value)
      return RemoteUsers::AvatarStruct.new if value.nil?

      # If it's already an AvatarStruct, return it
      return value if value.is_a?(RemoteUsers::AvatarStruct)

      # If the value is a JSON string, parse it
      if value.is_a?(String)
        begin
          value = JSON.parse(value)
        rescue JSON::ParserError
          return RemoteUsers::AvatarStruct.new
        end
      end

      # Convert hash to AvatarStruct
      if value.is_a?(Hash)
        return RemoteUsers::AvatarStruct.new(value)
      end

      # Default fallback
      RemoteUsers::AvatarStruct.new
    end
  end
end
